#!/usr/bin/env python3
"""
Simple script to reset admin password
"""

import os
import sys
from flask import Flask
from models import db, User
from config import config

def reset_admin_password():
    """Reset admin password to admin123"""
    try:
        # Create Flask app
        app = Flask(__name__)
        app.config.from_object(config['production'])
        
        # Initialize database
        db.init_app(app)
        
        with app.app_context():
            # Find admin user
            admin = User.query.filter_by(employee_number='ADMIN001').first()
            if not admin:
                print("Admin user not found!")
                return False
            
            # Reset password
            admin.set_password('admin123')
            admin.force_password_change = False
            admin.is_active = True
            admin.failed_login_attempts = 0
            admin.last_failed_login = None
            
            db.session.commit()
            print(f"Admin password reset successfully for user: {admin.name} ({admin.employee_number})")
            return True
            
    except Exception as e:
        print(f"Error resetting admin password: {str(e)}")
        return False

if __name__ == '__main__':
    if reset_admin_password():
        print("Password reset completed successfully!")
        sys.exit(0)
    else:
        print("Password reset failed!")
        sys.exit(1)
